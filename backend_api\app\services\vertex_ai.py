"""
Vertex AI service for TradingIA Backend.

This module provides integration with Google Cloud Vertex AI,
specifically for the Gemini model with function calling capabilities.
"""

import os
import json
import vertexai
from vertexai.generative_models import GenerativeModel, Tool, FunctionDeclaration
from google.oauth2 import service_account
from fastapi import HTTPException, status
from app.config import settings
from typing import List, Dict, Any, Optional
import logging

# Configure logging
logger = logging.getLogger(__name__)


def initialize_gemini_model(tools: List[Dict[str, Any]]) -> Optional[GenerativeModel]:
    """
    Initialize and configure the Gemini model with available tools.

    This function sets up the Vertex AI client and creates a GenerativeModel
    instance with the specified tools for function calling.

    Args:
        tools (List[Dict[str, Any]]): List of tool definitions for the model

    Returns:
        GenerativeModel: Configured Gemini model instance or None if not available

    Raises:
        HTTPException: If model initialization fails critically
    """
    credentials = None

    # Primero, intenta cargar credenciales desde el archivo JSON directamente
    credentials_file_path = os.path.join(os.path.dirname(__file__), "..", "..", "google-credentials.json")
    if os.path.exists(credentials_file_path):
        try:
            credentials = service_account.Credentials.from_service_account_file(credentials_file_path)
            logger.info("Successfully loaded credentials from service account file.")
        except Exception as e:
            logger.error(f"Failed to load credentials from service account file: {e}")

    # Si no se encontró el archivo, intenta cargar desde la variable de entorno JSON
    if credentials is None:
        credentials_json_str = settings.google_credentials_json
        if credentials_json_str:
            try:
                credentials_info = json.loads(credentials_json_str)
                credentials = service_account.Credentials.from_service_account_info(credentials_info)
                logger.info("Successfully loaded credentials from environment variable.")
            except Exception as e:
                logger.error(f"Failed to load credentials from JSON environment variable: {e}")

    # Si no se cargaron credenciales, la librería intentará los métodos por defecto

    try:
        # Initialize Vertex AI
        vertexai.init(
            project=settings.vertex_ai_project,
            location=settings.vertex_ai_location,
            credentials=credentials  # Pasa las credenciales aquí
        )

        # Convert tool definitions to Vertex AI format
        vertex_tools = []
        if tools:
            function_declarations = []

            for tool in tools:
                function_declaration = FunctionDeclaration(
                    name=tool["name"],
                    description=tool["description"],
                    parameters=tool.get("parameters", {})
                )
                function_declarations.append(function_declaration)
            
            if function_declarations:
                vertex_tools.append(Tool(function_declarations=function_declarations))
        
        # Create the model with tools
        model = GenerativeModel(
            model_name="gemini-1.5-flash-001",
            tools=vertex_tools if vertex_tools else None,
            system_instruction="""
            Eres un asistente financiero especializado en análisis de mercados.
            
            INSTRUCCIONES IMPORTANTES:
            1. Usa ÚNICAMENTE los datos devueltos por las herramientas disponibles
            2. NO inventes ni especules sobre datos financieros
            3. Siempre incluye un descargo: "Esta información es solo para fines educativos y no constituye asesoramiento financiero"
            4. Si necesitas datos específicos, usa las herramientas disponibles
            5. Sé preciso y profesional en tus respuestas
            6. Explica los indicadores técnicos de manera clara y educativa
            
            HERRAMIENTAS DISPONIBLES:
            - get_price_data: Para obtener datos históricos de precios
            - apply_indicator: Para calcular indicadores técnicos
            
            Responde siempre en español y de manera profesional.
            """
        )
        
        logger.info(f"Gemini model initialized successfully with {len(tools)} tools")
        return model
        
    except Exception as e:
        logger.error(f"Failed to initialize Gemini model: {str(e)}")
        logger.warning("Vertex AI not available, returning None for fallback mode")
        return None


async def generate_chat_response(
    messages: List[Dict[str, str]],
    model: Optional[GenerativeModel]
) -> Dict[str, Any]:
    """
    Generate a chat response using the Gemini model.

    This function sends the conversation history to Gemini and processes
    the response, handling both text responses and function calls.

    Args:
        messages (List[Dict[str, str]]): Conversation history
        model (GenerativeModel): Configured Gemini model instance or None

    Returns:
        Dict[str, Any]: Response containing either text or function call info

    Raises:
        HTTPException: If response generation fails
    """
    # Fallback response when Vertex AI is not available
    if model is None:
        logger.warning("Vertex AI model not available, returning fallback response")
        return {
            "type": "text",
            "content": "Lo siento, el servicio de IA no está disponible en este momento. Sin embargo, puedo ayudarte con datos financieros usando las herramientas disponibles. Por favor, especifica qué datos necesitas (por ejemplo: 'precio de AAPL' o 'RSI de Bitcoin')."
        }

    try:
        # Convert messages to Vertex AI format
        conversation_text = ""
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")

            if role == "user":
                conversation_text += f"Usuario: {content}\n"
            elif role == "assistant":
                conversation_text += f"Asistente: {content}\n"

        # Generate response
        logger.info(f"Sending to Gemini: {conversation_text[:200]}...")
        response = model.generate_content(conversation_text)
        logger.info(f"Gemini response received: {response}")

        # Check if the response contains function calls
        if response.candidates and response.candidates[0].content.parts:
            logger.info(f"Response has {len(response.candidates[0].content.parts)} parts")
            for i, part in enumerate(response.candidates[0].content.parts):
                logger.info(f"Processing part {i}: {type(part)}")
                # Check for function call
                if hasattr(part, 'function_call') and part.function_call:
                    function_call = part.function_call

                    # Extract function call details
                    function_name = function_call.name
                    function_args = {}

                    # Convert function arguments
                    if function_call.args:
                        for key, value in function_call.args.items():
                            function_args[key] = value

                    logger.info(f"Function call requested: {function_name}")

                    return {
                        "type": "function_call",
                        "function_name": function_name,
                        "function_args": function_args
                    }

                # Check for text response
                elif hasattr(part, 'text') and part.text:
                    logger.info("Text response generated successfully")

                    return {
                        "type": "text",
                        "content": part.text
                    }

                # Check for thought_signature (Gemini 2.5 Pro thinking mode)
                elif hasattr(part, 'thought_signature'):
                    logger.info("Thought signature detected - model is in thinking mode")
                    # For thought signatures, we need to prompt the model for a final response
                    return {
                        "type": "text",
                        "content": "Estoy procesando tu consulta. Por favor, reformula tu pregunta de manera más específica para obtener una mejor respuesta."
                    }

        # Fallback if no valid response found
        logger.warning("No valid response generated by model")
        logger.warning(f"Response structure: candidates={bool(response.candidates)}")
        if response.candidates:
            logger.warning(f"First candidate content: {response.candidates[0].content}")
        return {
            "type": "text",
            "content": "Lo siento, no pude generar una respuesta válida. Por favor, intenta reformular tu pregunta."
        }

    except Exception as e:
        logger.error(f"Error generating chat response: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate AI response"
        )


async def send_function_result_to_model(
    model: Optional[GenerativeModel],
    conversation_history: str,
    function_name: str,
    function_result: Any
) -> str:
    """
    Send function execution result back to the model for final response.

    Args:
        model (GenerativeModel): Configured Gemini model instance
        conversation_history (str): Previous conversation context
        function_name (str): Name of the executed function
        function_result (Any): Result from the function execution

    Returns:
        str: Final text response from the model

    Raises:
        HTTPException: If response generation fails
    """
    # Fallback when model is not available
    if model is None:
        logger.warning("Vertex AI model not available for function result processing")
        return f"Datos obtenidos de {function_name}: {json.dumps(function_result, indent=2, ensure_ascii=False)}. Esta información es solo para fines educativos y no constituye asesoramiento financiero."

    try:
        # Format the function result for the model
        result_text = f"""
{conversation_history}

Resultado de la función {function_name}:
{json.dumps(function_result, indent=2, ensure_ascii=False)}

Por favor, analiza estos datos y proporciona una respuesta clara y útil al usuario en español.
Recuerda incluir el descargo de responsabilidad sobre asesoramiento financiero.
"""

        # Generate final response
        response = model.generate_content(result_text)

        if response.candidates and response.candidates[0].content.parts:
            for part in response.candidates[0].content.parts:
                if hasattr(part, 'text') and part.text:
                    return part.text

        # Fallback response
        return "He procesado la información solicitada, pero no pude generar una respuesta clara. Esta información es solo para fines educativos y no constituye asesoramiento financiero."

    except Exception as e:
        logger.error(f"Error sending function result to model: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process function result"
        )
