"""
Chat routes for TradingIA Backend.

This module defines the main chat endpoint that orchestrates
authentication, AI interaction, and tool execution.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.models.chat import Chat<PERSON><PERSON><PERSON>, ChatResponse
from app.services.supabase_client import validate_user_token, save_chat_history
from app.services.vertex_ai import initialize_gemini_model, generate_chat_response, send_function_result_to_model
from app.tools.tradingview_provider import get_available_tools, get_price_data, apply_indicator
from typing import Dict, Any
import logging
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/chat", tags=["chat"])

# Security scheme
security = HTTPBearer()


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """
    Dependency to validate user authentication.
    
    This function extracts and validates the JWT token from the
    Authorization header and returns user information.
    
    Args:
        credentials: HTTP Bearer credentials from the request header
        
    Returns:
        Dict[str, Any]: User information if token is valid
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Validate the token using Supabase
        user_info = await validate_user_token(credentials.credentials)
        return user_info
        
    except HTTPException:
        # Re-raise authentication errors
        raise
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"}
        )


@router.post("/", response_model=ChatResponse)
async def chat_endpoint(
    request: ChatRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> ChatResponse:
    """
    Main chat endpoint for AI interactions.
    
    This endpoint handles user chat requests, validates authentication,
    processes the request through the AI model, executes any required
    tools, and returns the final response.
    
    Args:
        request: Chat request containing message and history
        current_user: Authenticated user information
        
    Returns:
        ChatResponse: AI-generated response
        
    Raises:
        HTTPException: If processing fails
    """
    try:
        logger.info(f"Chat request from user: {current_user.get('email', 'unknown')}")

        # Initialize Gemini model with available tools
        available_tools = get_available_tools()
        model = initialize_gemini_model(available_tools)

        # Prepare conversation history
        conversation_messages = []

        # Add history messages
        for msg in request.history:
            conversation_messages.append({
                "role": msg.role,
                "content": msg.content
            })

        # Add current message
        conversation_messages.append({
            "role": "user",
            "content": request.message
        })

        # Generate initial response from AI
        ai_response = await generate_chat_response(conversation_messages, model)

        # Handle tool calls if needed
        max_iterations = 3  # Prevent infinite loops
        iteration = 0

        while ai_response.get("type") == "function_call" and iteration < max_iterations:
            iteration += 1
            logger.info(f"Processing function call iteration {iteration}")

            function_name = ai_response["function_name"]
            function_args = ai_response["function_args"]

            # Execute the requested function
            try:
                if function_name == "get_price_data":
                    function_result = get_price_data(
                        symbol=function_args.get("symbol"),
                        interval=function_args.get("interval"),
                        n_bars=function_args.get("n_bars")
                    )
                elif function_name == "apply_indicator":
                    function_result = apply_indicator(
                        symbol=function_args.get("symbol"),
                        interval=function_args.get("interval"),
                        indicator_name=function_args.get("indicator_name"),
                        parameters=function_args.get("parameters", {})
                    )
                else:
                    logger.warning(f"Unknown function requested: {function_name}")
                    function_result = {"error": f"Function {function_name} not implemented"}

                # Send function result back to model for final response
                conversation_text = ""
                for msg in conversation_messages:
                    role = msg.get("role", "user")
                    content = msg.get("content", "")
                    if role == "user":
                        conversation_text += f"Usuario: {content}\n"
                    elif role == "assistant":
                        conversation_text += f"Asistente: {content}\n"

                final_response = await send_function_result_to_model(
                    model=model,
                    conversation_history=conversation_text,
                    function_name=function_name,
                    function_result=function_result
                )

                # Break the loop with final text response
                ai_response = {
                    "type": "text",
                    "content": final_response
                }

            except Exception as e:
                logger.error(f"Error executing function {function_name}: {str(e)}")
                ai_response = {
                    "type": "text",
                    "content": f"Lo siento, hubo un error al procesar tu solicitud: {str(e)}. Esta información es solo para fines educativos y no constituye asesoramiento financiero."
                }
                break

        # Get final response content
        if ai_response.get("type") == "text":
            final_reply = ai_response["content"]
        else:
            final_reply = "Lo siento, no pude procesar tu solicitud completamente. Esta información es solo para fines educativos y no constituye asesoramiento financiero."

        # Create response object
        conversation_id = f"conv_{current_user['id'][:8]}"
        response = ChatResponse(
            reply=final_reply,
            timestamp=datetime.now(),
            conversation_id=conversation_id
        )

        # Save chat history to database
        try:
            # Prepare request messages with proper datetime serialization
            request_messages_to_save = []
            for msg in request.history:
                msg_dict = msg.dict()
                # Convert datetime to ISO string if present
                if msg_dict.get('timestamp') and hasattr(msg_dict['timestamp'], 'isoformat'):
                    msg_dict['timestamp'] = msg_dict['timestamp'].isoformat()
                request_messages_to_save.append(msg_dict)

            # Add current user message with timestamp
            user_message_to_save = {
                "role": "user",
                "content": request.message,
                "timestamp": datetime.now().isoformat()
            }
            request_messages_to_save.append(user_message_to_save)

            await save_chat_history(
                user_id=current_user["id"],
                request_messages=request_messages_to_save,
                ai_response=final_reply,
                conversation_id=conversation_id
            )
            logger.info(f"Chat history saved for user: {current_user.get('email', 'unknown')}")
        except Exception as e:
            # Log error but don't fail the request
            logger.error(f"Failed to save chat history: {str(e)}")

        return response
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error processing chat request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process chat request"
        )


@router.get("/health")
async def chat_health():
    """Health check endpoint for the chat service."""
    return {
        "status": "healthy",
        "service": "chat",
        "timestamp": datetime.now().isoformat()
    }
